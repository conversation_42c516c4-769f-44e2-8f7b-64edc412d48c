"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/crew-training/page",{

/***/ "(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx":
/*!*************************************************************!*\
  !*** ./src/app/ui/crew-training/unified-training-table.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnifiedTrainingTable: function() { return /* binding */ UnifiedTrainingTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@3.6.0/node_modules/date-fns/format.mjs\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _vessels_list__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../vessels/list */ \"(app-pages-browser)/./src/app/ui/vessels/list.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/app/ui/crew-training/utils/crew-training-utils */ \"(app-pages-browser)/./src/app/ui/crew-training/utils/crew-training-utils.ts\");\n/* harmony import */ var _reactuses_core__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @reactuses/core */ \"(app-pages-browser)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\");\n/* harmony import */ var _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/hooks/useBreakpoints */ \"(app-pages-browser)/./src/components/hooks/useBreakpoints.tsx\");\n/* __next_internal_client_entry_do_not_use__ UnifiedTrainingTable,default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to format dates using date-fns\nconst formatDate = (dateString)=>{\n    if (!dateString) return \"\";\n    try {\n        const date = new Date(dateString);\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(date, \"dd/MM/yy\");\n    } catch (e) {\n        return \"\";\n    }\n};\nconst getRowStatus = (rowData)=>{\n    if (rowData.status.isOverdue) {\n        return \"overdue\";\n    }\n    if (rowData.status.dueWithinSevenDays) {\n        return \"upcoming\";\n    }\n    return \"normal\";\n};\n// Status-based color classes for training titles\nconst getStatusColorClasses = (training)=>{\n    if (training.status.isOverdue) {\n        return \"text-destructive/80 hover:text-destructive\";\n    }\n    if (training.status.dueWithinSevenDays) {\n        return \"text-warning/80 hover:text-warning\";\n    }\n    return \"hover:text-curious-blue-400\";\n};\nconst UnifiedMobileTrainingCard = (param)=>{\n    let { data } = param;\n    var _data_trainingType, _data_originalData_trainingTypes, _data_originalData, _data_vessel, _data_originalData1, _data_originalData2;\n    _s();\n    const bp = (0,_components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints)();\n    const isCompleted = data.category === \"completed\";\n    const isOverdue = data.category === \"overdue\";\n    const members = data.members || [];\n    const trainingTitle = ((_data_trainingType = data.trainingType) === null || _data_trainingType === void 0 ? void 0 : _data_trainingType.title) || \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full space-y-2.5 tablet-md:border-none py-3\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-wrap justify-between items-center\",\n                children: [\n                    isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: \"/crew-training/info?id=\".concat(data.id),\n                        className: \"font-semibold text-base hover:text-primary\",\n                        children: formatDate(data.dueDate)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 21\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"font-semibold text-base\", getStatusColorClasses(data)),\n                        children: formatDate(data.dueDate)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 21\n                    }, undefined),\n                    !bp.landscape && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.StatusBadge, {\n                        isOverdue: data.status.isOverdue,\n                        isUpcoming: data.status.dueWithinSevenDays,\n                        label: data.status.label || data.category\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 86,\n                columnNumber: 13\n            }, undefined),\n            !bp.laptop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-sm line-clamp-2\",\n                children: isCompleted ? ((_data_originalData = data.originalData) === null || _data_originalData === void 0 ? void 0 : (_data_originalData_trainingTypes = _data_originalData.trainingTypes) === null || _data_originalData_trainingTypes === void 0 ? void 0 : _data_originalData_trainingTypes.nodes) ? data.originalData.trainingTypes.nodes.map((item)=>item.title).join(\", \") : trainingTitle : trainingTitle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 115,\n                columnNumber: 17\n            }, undefined),\n            !bp[\"tablet-md\"] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: isCompleted ? \"Location:\" : \"Vessel:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_data_vessel = data.vessel) === null || _data_vessel === void 0 ? void 0 : _data_vessel.title) || ((_data_originalData1 = data.originalData) === null || _data_originalData1 === void 0 ? void 0 : _data_originalData1.trainingLocationType) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                                vessel: data.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 128,\n                columnNumber: 17\n            }, undefined),\n            !bp[\"tablet-lg\"] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                position: \"left\",\n                className: \"text-sm text-muted-foreground\",\n                label: isCompleted ? \"Team:\" : \"Crew Members:\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-1 flex-wrap\",\n                    children: [\n                        members.slice(0, bp[\"tablet-md\"] ? 8 : 6).map((member)=>/*#__PURE__*/ {\n                            var _member_surname;\n                            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                            size: \"sm\",\n                                            variant: !isCompleted && isOverdue ? \"destructive\" : \"secondary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                className: \"text-sm\",\n                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(member.firstName, member.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                        children: [\n                                            member.firstName,\n                                            \" \",\n                                            (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, member.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 33\n                            }, undefined);\n                        }),\n                        members.length > (bp[\"tablet-md\"] ? 8 : 6) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Popover, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.PopoverTrigger, {\n                                    className: \"w-fit\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"w-fit h-8\",\n                                        children: [\n                                            \"+\",\n                                            members.length - (bp[\"tablet-md\"] ? 8 : 6),\n                                            \" \",\n                                            \"more\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.PopoverContent, {\n                                    className: \"w-64\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 max-h-64 overflow-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2\",\n                                            children: members.slice(bp[\"tablet-md\"] ? 8 : 6).map((remainingMember)=>/*#__PURE__*/ {\n                                                var _remainingMember_firstName, _remainingMember_surname;\n                                                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                                            size: \"xs\",\n                                                            variant: \"secondary\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                                className: \"text-xs\",\n                                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(remainingMember.firstName, remainingMember.surname)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 61\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 57\n                                                        }, undefined),\n                                                        \"\".concat((_remainingMember_firstName = remainingMember.firstName) !== null && _remainingMember_firstName !== void 0 ? _remainingMember_firstName : \"\", \" \").concat((_remainingMember_surname = remainingMember.surname) !== null && _remainingMember_surname !== void 0 ? _remainingMember_surname : \"\")\n                                                    ]\n                                                }, remainingMember.id, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 53\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 29\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 148,\n                columnNumber: 17\n            }, undefined),\n            !bp[\"tablet-md\"] && isCompleted && ((_data_originalData2 = data.originalData) === null || _data_originalData2 === void 0 ? void 0 : _data_originalData2.trainer) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                        className: \"text-sm m-0 text-muted-foreground\",\n                        children: \"Trainer:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 21\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                size: \"sm\",\n                                variant: \"secondary\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                    className: \"text-sm\",\n                                    children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(data.originalData.trainer.firstName, data.originalData.trainer.surname)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm\",\n                                children: [\n                                    data.originalData.trainer.firstName,\n                                    \" \",\n                                    data.originalData.trainer.surname\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 21\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                lineNumber: 225,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n        lineNumber: 84,\n        columnNumber: 9\n    }, undefined);\n};\n_s(UnifiedMobileTrainingCard, \"ZxSHrfPd9jdclp97BkZrjvTJck4=\", false, function() {\n    return [\n        _components_hooks_useBreakpoints__WEBPACK_IMPORTED_MODULE_10__.useBreakpoints\n    ];\n});\n_c = UnifiedMobileTrainingCard;\nconst UnifiedTrainingTable = (param)=>{\n    let { trainingSessionDues = [], completedTrainingList = [], unifiedData: preFilteredData, getVesselWithIcon, includeCompleted = true, memberId, isVesselView = false, showToolbar = false, pageSize } = param;\n    _s1();\n    const isWide = (0,_reactuses_core__WEBPACK_IMPORTED_MODULE_12__.useMediaQuery)(\"(min-width: 720px)\");\n    // Use pre-filtered data if available, otherwise merge and sort data\n    const unifiedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (preFilteredData && Array.isArray(preFilteredData)) {\n            return preFilteredData;\n        }\n        return (0,_app_ui_crew_training_utils_crew_training_utils__WEBPACK_IMPORTED_MODULE_9__.mergeAndSortCrewTrainingData)({\n            trainingSessionDues,\n            completedTrainingList,\n            getVesselWithIcon,\n            includeCompleted\n        });\n    }, [\n        preFilteredData,\n        trainingSessionDues,\n        completedTrainingList,\n        getVesselWithIcon,\n        includeCompleted\n    ]);\n    // Determine if we have mixed data types or primarily one type\n    const hasOverdueOrUpcoming = unifiedData.some((item)=>item.category === \"overdue\" || item.category === \"upcoming\");\n    const hasCompleted = unifiedData.some((item)=>item.category === \"completed\");\n    // Create unified column structure for all training data types\n    const getUnifiedColumns = ()=>{\n        return [\n            // Mobile column - shows training card on mobile, adapts header based on data\n            {\n                accessorKey: \"title\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Date\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                cellAlignment: \"left\",\n                cellClassName: \"w-ful xs:w-auto\",\n                cell: (param)=>{\n                    let { row } = param;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UnifiedMobileTrainingCard, {\n                        data: training\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 28\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    // Sort by category priority first, then by date\n                    const trainingA = rowA.original;\n                    const trainingB = rowB.original;\n                    const priorityA = trainingA.category === \"overdue\" ? 1 : trainingA.category === \"upcoming\" ? 2 : 3;\n                    const priorityB = trainingB.category === \"overdue\" ? 1 : trainingB.category === \"upcoming\" ? 2 : 3;\n                    if (priorityA !== priorityB) {\n                        return priorityA - priorityB;\n                    }\n                    const dateA = new Date(trainingA.dueDate).getTime();\n                    const dateB = new Date(trainingB.dueDate).getTime();\n                    return trainingA.category === \"completed\" ? dateB - dateA : dateA - dateB;\n                }\n            },\n            // Training Type column - shows training types for all data types\n            {\n                accessorKey: \"trainingType\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Training/drill\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"laptop\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_originalData_trainingTypes, _training_originalData, _training_trainingType, _training_trainingType1;\n                    const training = row.original;\n                    const isCompleted = training.category === \"completed\";\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.P, {\n                        children: isCompleted ? ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : (_training_originalData_trainingTypes = _training_originalData.trainingTypes) === null || _training_originalData_trainingTypes === void 0 ? void 0 : _training_originalData_trainingTypes.nodes) ? training.originalData.trainingTypes.nodes.map((item)=>item.title).join(\", \") : ((_training_trainingType = training.trainingType) === null || _training_trainingType === void 0 ? void 0 : _training_trainingType.title) || \"\" : ((_training_trainingType1 = training.trainingType) === null || _training_trainingType1 === void 0 ? void 0 : _training_trainingType1.title) || \"\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_originalData_trainingTypes_nodes_, _rowA_original_originalData_trainingTypes_nodes, _rowA_original_originalData_trainingTypes, _rowA_original_originalData, _rowA_original, _rowA_original_trainingType, _rowA_original1, _rowB_original_originalData_trainingTypes_nodes_, _rowB_original_originalData_trainingTypes_nodes, _rowB_original_originalData_trainingTypes, _rowB_original_originalData, _rowB_original, _rowB_original_trainingType, _rowB_original1;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes = _rowA_original_originalData.trainingTypes) === null || _rowA_original_originalData_trainingTypes === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes_nodes = _rowA_original_originalData_trainingTypes.nodes) === null || _rowA_original_originalData_trainingTypes_nodes === void 0 ? void 0 : (_rowA_original_originalData_trainingTypes_nodes_ = _rowA_original_originalData_trainingTypes_nodes[0]) === null || _rowA_original_originalData_trainingTypes_nodes_ === void 0 ? void 0 : _rowA_original_originalData_trainingTypes_nodes_.title) || (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_trainingType = _rowA_original1.trainingType) === null || _rowA_original_trainingType === void 0 ? void 0 : _rowA_original_trainingType.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes = _rowB_original_originalData.trainingTypes) === null || _rowB_original_originalData_trainingTypes === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes_nodes = _rowB_original_originalData_trainingTypes.nodes) === null || _rowB_original_originalData_trainingTypes_nodes === void 0 ? void 0 : (_rowB_original_originalData_trainingTypes_nodes_ = _rowB_original_originalData_trainingTypes_nodes[0]) === null || _rowB_original_originalData_trainingTypes_nodes_ === void 0 ? void 0 : _rowB_original_originalData_trainingTypes_nodes_.title) || (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_trainingType = _rowB_original1.trainingType) === null || _rowB_original_trainingType === void 0 ? void 0 : _rowB_original_trainingType.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            // Vessel column - shows vessel information for all data types\n            {\n                accessorKey: \"vessel\",\n                cellAlignment: \"left\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: isVesselView ? \"\" : \"Where\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 394,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_vessel, _training_originalData;\n                    const training = row.original;\n                    if (isVesselView) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 32\n                        }, undefined);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-nowrap\",\n                                children: ((_training_vessel = training.vessel) === null || _training_vessel === void 0 ? void 0 : _training_vessel.title) || ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : _training_originalData.trainingLocationType) || \"\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 29\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_vessels_list__WEBPACK_IMPORTED_MODULE_5__.LocationModal, {\n                                vessel: training.vessel,\n                                iconClassName: \"size-8\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_vessel, _rowA_original, _rowB_original_vessel, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_vessel = _rowA_original.vessel) === null || _rowA_original_vessel === void 0 ? void 0 : _rowA_original_vessel.title) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_vessel = _rowB_original.vessel) === null || _rowB_original_vessel === void 0 ? void 0 : _rowB_original_vessel.title) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            // Crew column - shows crew members for all data types\n            {\n                accessorKey: \"crew\",\n                cellAlignment: \"right\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Who\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 433,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-lg\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_originalData_members, _training_originalData, _training_status;\n                    const training = row.original;\n                    const members = ((_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : (_training_originalData_members = _training_originalData.members) === null || _training_originalData_members === void 0 ? void 0 : _training_originalData_members.nodes) || training.members || [];\n                    return isWide ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-1\",\n                        children: members.map((member, index)=>/*#__PURE__*/ {\n                            var _member_surname;\n                            return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                        mobileClickable: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                            size: \"sm\",\n                                            variant: training.status.isOverdue ? \"destructive\" : \"secondary\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                className: \"text-sm\",\n                                                children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(member.firstName, member.surname)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 45\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                        children: [\n                                            member.firstName,\n                                            \" \",\n                                            (_member_surname = member.surname) !== null && _member_surname !== void 0 ? _member_surname : \"\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, member.id || index, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 33\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 444,\n                        columnNumber: 25\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"!rounded-full size-10 flex items-center justify-center text-sm font-medium\", (_training_status = training.status) === null || _training_status === void 0 ? void 0 : _training_status.class),\n                        children: members.length\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_originalData_members, _rowA_original_originalData, _rowA_original, _rowA_original1, _rowB_original_originalData_members, _rowB_original_originalData, _rowB_original, _rowB_original1, _membersA_, _membersA_1, _membersB_, _membersB_1;\n                    const membersA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_members = _rowA_original_originalData.members) === null || _rowA_original_originalData_members === void 0 ? void 0 : _rowA_original_originalData_members.nodes) || (rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : _rowA_original1.members) || [];\n                    const membersB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_members = _rowB_original_originalData.members) === null || _rowB_original_originalData_members === void 0 ? void 0 : _rowB_original_originalData_members.nodes) || (rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : _rowB_original1.members) || [];\n                    var _membersA__firstName, _membersA__surname;\n                    const valueA = \"\".concat((_membersA__firstName = membersA === null || membersA === void 0 ? void 0 : (_membersA_ = membersA[0]) === null || _membersA_ === void 0 ? void 0 : _membersA_.firstName) !== null && _membersA__firstName !== void 0 ? _membersA__firstName : \"\", \" \").concat((_membersA__surname = membersA === null || membersA === void 0 ? void 0 : (_membersA_1 = membersA[0]) === null || _membersA_1 === void 0 ? void 0 : _membersA_1.surname) !== null && _membersA__surname !== void 0 ? _membersA__surname : \"\") || \"\";\n                    var _membersB__firstName, _membersB__surname;\n                    const valueB = \"\".concat((_membersB__firstName = membersB === null || membersB === void 0 ? void 0 : (_membersB_ = membersB[0]) === null || _membersB_ === void 0 ? void 0 : _membersB_.firstName) !== null && _membersB__firstName !== void 0 ? _membersB__firstName : \"\", \" \").concat((_membersB__surname = membersB === null || membersB === void 0 ? void 0 : (_membersB_1 = membersB[0]) === null || _membersB_1 === void 0 ? void 0 : _membersB_1.surname) !== null && _membersB__surname !== void 0 ? _membersB__surname : \"\") || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            // Trainer column - shows trainer for completed training, dash for others\n            {\n                accessorKey: \"trainer\",\n                cellAlignment: \"center\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Trainer\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 503,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"tablet-md\",\n                cell: (param)=>{\n                    let { row } = param;\n                    var _training_originalData;\n                    const training = row.original;\n                    const trainer = (_training_originalData = training.originalData) === null || _training_originalData === void 0 ? void 0 : _training_originalData.trainer;\n                    if (!trainer || training.category !== \"completed\") {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground\",\n                            children: \"-\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 29\n                        }, undefined);\n                    }\n                    var _trainer_surname;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-nowrap\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Tooltip, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipTrigger, {\n                                    mobileClickable: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                        size: \"sm\",\n                                        variant: \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                            className: \"text-sm\",\n                                            children: (0,_components_ui__WEBPACK_IMPORTED_MODULE_3__.getCrewInitials)(trainer.firstName, trainer.surname)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.TooltipContent, {\n                                    children: [\n                                        trainer.firstName,\n                                        \" \",\n                                        (_trainer_surname = trainer.surname) !== null && _trainer_surname !== void 0 ? _trainer_surname : \"\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                                    lineNumber: 531,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_originalData_trainer, _rowA_original_originalData, _rowA_original, _rowA_original_originalData_trainer1, _rowA_original_originalData1, _rowA_original1, _rowB_original_originalData_trainer, _rowB_original_originalData, _rowB_original, _rowB_original_originalData_trainer1, _rowB_original_originalData1, _rowB_original1;\n                    const valueA = \"\".concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_originalData = _rowA_original.originalData) === null || _rowA_original_originalData === void 0 ? void 0 : (_rowA_original_originalData_trainer = _rowA_original_originalData.trainer) === null || _rowA_original_originalData_trainer === void 0 ? void 0 : _rowA_original_originalData_trainer.firstName) || \"\", \" \").concat((rowA === null || rowA === void 0 ? void 0 : (_rowA_original1 = rowA.original) === null || _rowA_original1 === void 0 ? void 0 : (_rowA_original_originalData1 = _rowA_original1.originalData) === null || _rowA_original_originalData1 === void 0 ? void 0 : (_rowA_original_originalData_trainer1 = _rowA_original_originalData1.trainer) === null || _rowA_original_originalData_trainer1 === void 0 ? void 0 : _rowA_original_originalData_trainer1.surname) || \"\") || \"\";\n                    const valueB = \"\".concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_originalData = _rowB_original.originalData) === null || _rowB_original_originalData === void 0 ? void 0 : (_rowB_original_originalData_trainer = _rowB_original_originalData.trainer) === null || _rowB_original_originalData_trainer === void 0 ? void 0 : _rowB_original_originalData_trainer.firstName) || \"\", \" \").concat((rowB === null || rowB === void 0 ? void 0 : (_rowB_original1 = rowB.original) === null || _rowB_original1 === void 0 ? void 0 : (_rowB_original_originalData1 = _rowB_original1.originalData) === null || _rowB_original_originalData1 === void 0 ? void 0 : (_rowB_original_originalData_trainer1 = _rowB_original_originalData1.trainer) === null || _rowB_original_originalData_trainer1 === void 0 ? void 0 : _rowB_original_originalData_trainer1.surname) || \"\") || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            },\n            // Status column - shows status badge at the end of the row\n            {\n                accessorKey: \"status\",\n                cellAlignment: \"right\",\n                header: (param)=>{\n                    let { column } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_8__.DataTableSortHeader, {\n                        column: column,\n                        title: \"Status\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 553,\n                        columnNumber: 21\n                    }, undefined);\n                },\n                breakpoint: \"landscape\",\n                cell: (param)=>{\n                    let { row } = param;\n                    const training = row.original;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_3__.StatusBadge, {\n                        isOverdue: training.status.isOverdue,\n                        isUpcoming: training.status.dueWithinSevenDays,\n                        label: training.status.label || training.category\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n                        lineNumber: 559,\n                        columnNumber: 25\n                    }, undefined);\n                },\n                sortingFn: (rowA, rowB)=>{\n                    var _rowA_original_status, _rowA_original, _rowB_original_status, _rowB_original;\n                    const valueA = (rowA === null || rowA === void 0 ? void 0 : (_rowA_original = rowA.original) === null || _rowA_original === void 0 ? void 0 : (_rowA_original_status = _rowA_original.status) === null || _rowA_original_status === void 0 ? void 0 : _rowA_original_status.label) || \"\";\n                    const valueB = (rowB === null || rowB === void 0 ? void 0 : (_rowB_original = rowB.original) === null || _rowB_original === void 0 ? void 0 : (_rowB_original_status = _rowB_original.status) === null || _rowB_original_status === void 0 ? void 0 : _rowB_original_status.label) || \"\";\n                    return valueA.localeCompare(valueB);\n                }\n            }\n        ];\n    };\n    // Create table columns\n    const columns = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.createColumns)(getUnifiedColumns()), [\n        hasOverdueOrUpcoming,\n        hasCompleted,\n        isVesselView,\n        isWide\n    ]);\n    if (!(unifiedData === null || unifiedData === void 0 ? void 0 : unifiedData.length)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8 text-muted-foreground\",\n            children: \"No training data available\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n            lineNumber: 587,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_7__.FilteredTable, {\n        columns: columns,\n        data: unifiedData,\n        showToolbar: showToolbar,\n        rowStatus: getRowStatus,\n        pageSize: pageSize || 20\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\crew-training\\\\unified-training-table.tsx\",\n        lineNumber: 594,\n        columnNumber: 9\n    }, undefined);\n};\n_s1(UnifiedTrainingTable, \"x7VOLZGRwr6UzHBlctBEB/GNMsI=\", false, function() {\n    return [\n        _reactuses_core__WEBPACK_IMPORTED_MODULE_12__.useMediaQuery\n    ];\n});\n_c1 = UnifiedTrainingTable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (UnifiedTrainingTable);\nvar _c, _c1;\n$RefreshReg$(_c, \"UnifiedMobileTrainingCard\");\n$RefreshReg$(_c1, \"UnifiedTrainingTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/crew-training/unified-training-table.tsx\n"));

/***/ })

});