"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/maintenance/page",{

/***/ "(app-pages-browser)/./src/components/ui/tooltip.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/tooltip.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tooltip: function() { return /* binding */ Tooltip; },\n/* harmony export */   TooltipContent: function() { return /* binding */ TooltipContent; },\n/* harmony export */   TooltipProvider: function() { return /* binding */ TooltipProvider; },\n/* harmony export */   TooltipTrigger: function() { return /* binding */ TooltipTrigger; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-tooltip */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-tooltip@1.2_50e390c4dabde08ed3112eb9f58da500/node_modules/@radix-ui/react-tooltip/dist/index.mjs\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Tooltip,TooltipTrigger,TooltipContent,TooltipProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$();\n\n\n\nconst TooltipProvider = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Provider;\n// Hook to detect touch devices\nconst useTouchDevice = ()=>{\n    _s();\n    const [isTouchDevice, setIsTouchDevice] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        const checkTouchDevice = ()=>{\n            setIsTouchDevice(\"ontouchstart\" in window || navigator.maxTouchPoints > 0 || window.matchMedia(\"(pointer: coarse)\").matches);\n        };\n        checkTouchDevice();\n        // Listen for changes in pointer capability\n        const mediaQuery = window.matchMedia(\"(pointer: coarse)\");\n        const handleChange = ()=>checkTouchDevice();\n        mediaQuery.addEventListener(\"change\", handleChange);\n        return ()=>mediaQuery.removeEventListener(\"change\", handleChange);\n    }, []);\n    return isTouchDevice;\n};\n_s(useTouchDevice, \"qO9r2BnValft3C3z7jhU/3KyGPI=\");\n// Hook to handle click outside\nconst useClickOutside = function(ref, callback) {\n    let enabled = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n    _s1();\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        if (!enabled) return;\n        const handleClickOutside = (event)=>{\n            if (ref.current && !ref.current.contains(event.target)) {\n                callback();\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        document.addEventListener(\"touchstart\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n            document.removeEventListener(\"touchstart\", handleClickOutside);\n        };\n    }, [\n        ref,\n        callback,\n        enabled\n    ]);\n};\n_s1(useClickOutside, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n// Enhanced Tooltip component with optional mobile click support\nconst Tooltip = /*#__PURE__*/ _s2(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = _s2((param, _ref)=>{\n    let { mobileClickable = false, ...props } = param;\n    _s2();\n    const [isOpen, setIsOpen] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    const isTouchDevice = useTouchDevice();\n    const tooltipRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null);\n    // Handle click outside to close tooltip on mobile\n    useClickOutside(tooltipRef, ()=>setIsOpen(false), mobileClickable && isTouchDevice && isOpen);\n    // For touch devices with mobileClickable enabled, use controlled mode\n    if (mobileClickable && isTouchDevice) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: tooltipRef,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Root, {\n                open: isOpen,\n                onOpenChange: setIsOpen,\n                ...props\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\tooltip.tsx\",\n                lineNumber: 83,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\tooltip.tsx\",\n            lineNumber: 82,\n            columnNumber: 13\n        }, undefined);\n    }\n    // Default behavior for non-touch devices or when mobileClickable is false\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\tooltip.tsx\",\n        lineNumber: 93,\n        columnNumber: 12\n    }, undefined);\n}, \"3Vhm1HGOIaczrYFB8rqP76ZEr2I=\", false, function() {\n    return [\n        useTouchDevice,\n        useClickOutside\n    ];\n})), \"3Vhm1HGOIaczrYFB8rqP76ZEr2I=\", false, function() {\n    return [\n        useTouchDevice,\n        useClickOutside\n    ];\n});\n_c1 = Tooltip;\nTooltip.displayName = \"Tooltip\";\nconst TooltipTrigger = /*#__PURE__*/ _s3(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c2 = _s3((param, ref)=>{\n    let { mobileClickable = false, onClick, ...props } = param;\n    _s3();\n    const isTouchDevice = useTouchDevice();\n    const handleClick = react__WEBPACK_IMPORTED_MODULE_1__.useCallback((event)=>{\n        // Call original onClick if provided\n        onClick === null || onClick === void 0 ? void 0 : onClick(event);\n        // For touch devices with mobileClickable, toggle tooltip\n        if (mobileClickable && isTouchDevice) {\n        // The tooltip state is managed by the Root component\n        // This click will trigger the onOpenChange in the Root\n        }\n    }, [\n        onClick,\n        mobileClickable,\n        isTouchDevice\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        ref: ref,\n        onClick: handleClick,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\tooltip.tsx\",\n        lineNumber: 120,\n        columnNumber: 9\n    }, undefined);\n}, \"Nz+fFl5pBT5PQWSz194kpoFQBfg=\", false, function() {\n    return [\n        useTouchDevice\n    ];\n})), \"Nz+fFl5pBT5PQWSz194kpoFQBfg=\", false, function() {\n    return [\n        useTouchDevice\n    ];\n});\n_c3 = TooltipTrigger;\nTooltipTrigger.displayName = \"TooltipTrigger\";\nconst TooltipContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c4 = (param, ref)=>{\n    let { className, sideOffset = 4, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 overflow-hidden px-3 py-1.5  text-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 bg-fire-bush-100 text-fire-bush-600 rounded-md border border-fire-bush-500\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\tooltip.tsx\",\n            lineNumber: 130,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\tooltip.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, undefined);\n});\n_c5 = TooltipContent;\nTooltipContent.displayName = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\n\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"Tooltip$React.forwardRef\");\n$RefreshReg$(_c1, \"Tooltip\");\n$RefreshReg$(_c2, \"TooltipTrigger$React.forwardRef\");\n$RefreshReg$(_c3, \"TooltipTrigger\");\n$RefreshReg$(_c4, \"TooltipContent$React.forwardRef\");\n$RefreshReg$(_c5, \"TooltipContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/tooltip.tsx\n"));

/***/ })

});