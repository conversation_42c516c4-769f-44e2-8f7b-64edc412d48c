"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/maintenance/layout",{

/***/ "(app-pages-browser)/./src/components/ui/tooltip.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/tooltip.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tooltip: function() { return /* binding */ Tooltip; },\n/* harmony export */   TooltipContent: function() { return /* binding */ TooltipContent; },\n/* harmony export */   TooltipProvider: function() { return /* binding */ TooltipProvider; },\n/* harmony export */   TooltipTrigger: function() { return /* binding */ TooltipTrigger; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-tooltip */ \"(app-pages-browser)/./node_modules/.pnpm/@radix-ui+react-tooltip@1.2_50e390c4dabde08ed3112eb9f58da500/node_modules/@radix-ui/react-tooltip/dist/index.mjs\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Tooltip,TooltipTrigger,TooltipContent,TooltipProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst TooltipProvider = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Provider;\n// Enhanced Tooltip component with optional mobile click support\nconst Tooltip = /*#__PURE__*/ _s(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = _s((param, ref)=>{\n    let { mobileClickable = false, ...props } = param;\n    _s();\n    const [isOpen, setIsOpen] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    const [isTouchDevice, setIsTouchDevice] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    // Detect touch device capability\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        const checkTouchDevice = ()=>{\n            setIsTouchDevice(\"ontouchstart\" in window || navigator.maxTouchPoints > 0 || window.matchMedia(\"(pointer: coarse)\").matches);\n        };\n        checkTouchDevice();\n        // Listen for changes in pointer capability\n        const mediaQuery = window.matchMedia(\"(pointer: coarse)\");\n        const handleChange = ()=>checkTouchDevice();\n        if (mediaQuery.addEventListener) {\n            mediaQuery.addEventListener(\"change\", handleChange);\n            return ()=>mediaQuery.removeEventListener(\"change\", handleChange);\n        } else {\n            // Fallback for older browsers\n            mediaQuery.addListener(handleChange);\n            return ()=>mediaQuery.removeListener(handleChange);\n        }\n    }, []);\n    // For touch devices with mobileClickable enabled, use controlled mode\n    if (mobileClickable && isTouchDevice) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Root, {\n            ref: ref,\n            open: isOpen,\n            onOpenChange: setIsOpen,\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\tooltip.tsx\",\n            lineNumber: 49,\n            columnNumber: 13\n        }, undefined);\n    }\n    // Default behavior for non-touch devices or when mobileClickable is false\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\tooltip.tsx\",\n        lineNumber: 59,\n        columnNumber: 12\n    }, undefined);\n}, \"Sc+lUN8GnTdb4X6aHFORE5HuWoY=\")), \"Sc+lUN8GnTdb4X6aHFORE5HuWoY=\");\n_c1 = Tooltip;\nTooltip.displayName = \"Tooltip\";\nconst TooltipTrigger = /*#__PURE__*/ _s1(react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c2 = _s1((param, ref)=>{\n    let { mobileClickable = false, onClick, ...props } = param;\n    _s1();\n    const [isTouchDevice, setIsTouchDevice] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    // Detect touch device capability\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(()=>{\n        const checkTouchDevice = ()=>{\n            setIsTouchDevice(\"ontouchstart\" in window || navigator.maxTouchPoints > 0 || window.matchMedia(\"(pointer: coarse)\").matches);\n        };\n        checkTouchDevice();\n        const mediaQuery = window.matchMedia(\"(pointer: coarse)\");\n        const handleChange = ()=>checkTouchDevice();\n        if (mediaQuery.addEventListener) {\n            mediaQuery.addEventListener(\"change\", handleChange);\n            return ()=>mediaQuery.removeEventListener(\"change\", handleChange);\n        } else {\n            mediaQuery.addListener(handleChange);\n            return ()=>mediaQuery.removeListener(handleChange);\n        }\n    }, []);\n    const handleClick = react__WEBPACK_IMPORTED_MODULE_1__.useCallback((event)=>{\n        // Call original onClick if provided\n        onClick === null || onClick === void 0 ? void 0 : onClick(event);\n    // For touch devices with mobileClickable, the tooltip state is handled by the Root component\n    // No additional click handling needed here as Radix handles it\n    }, [\n        onClick\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        ref: ref,\n        onClick: handleClick,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\tooltip.tsx\",\n        lineNumber: 107,\n        columnNumber: 9\n    }, undefined);\n}, \"xewP7YJ9KuoMLyexVUUggyyihK8=\")), \"xewP7YJ9KuoMLyexVUUggyyihK8=\");\n_c3 = TooltipTrigger;\nTooltipTrigger.displayName = \"TooltipTrigger\";\nconst TooltipContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c4 = (param, ref)=>{\n    let { className, sideOffset = 4, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 overflow-hidden px-3 py-1.5  text-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 bg-fire-bush-100 text-fire-bush-600 rounded-md border border-fire-bush-500\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\tooltip.tsx\",\n            lineNumber: 117,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\ui\\\\tooltip.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, undefined);\n});\n_c5 = TooltipContent;\nTooltipContent.displayName = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\n\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"Tooltip$React.forwardRef\");\n$RefreshReg$(_c1, \"Tooltip\");\n$RefreshReg$(_c2, \"TooltipTrigger$React.forwardRef\");\n$RefreshReg$(_c3, \"TooltipTrigger\");\n$RefreshReg$(_c4, \"TooltipContent$React.forwardRef\");\n$RefreshReg$(_c5, \"TooltipContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/tooltip.tsx\n"));

/***/ })

});